# Docker Networking Fix for Chartbrew-Strapi Authentication

## 🔍 Current Docker Network Configuration

Your Chartbrew container is running with IP address: **************

This is on the default Docker bridge network (**********/16), not the custom subnet mentioned in your Docker settings (***********/24).

## 🚨 The Problem

The authentication issue between Strapi and Chartbrew is caused by:

1. **Network Isolation**: Docker containers by default can't be accessed via `localhost` from the host or other containers
2. **Incorrect IP Configuration**: The Strapi plugin is trying to connect to Chartbrew using the wrong IP address
3. **API Binding**: The Chartbrew API is binding only to localhost inside the container

## 🔧 Solution Options

### Option 1: Use Docker Container IP (Implemented)

We've updated your Strapi plugin configuration to use the actual Docker container IP:

```javascript
// In backend/config/plugins.ts
chartbrew: {
  enabled: true,
  config: {
    apiUrl: 'http://**********:4019',  // Chartbrew container IP
    frontendUrl: 'http://**********:4018',
  }
},
```

**Important**: The Chartbrew API must bind to all interfaces (0.0.0.0) for this to work:

```env
# In Chartbrew .env file
CB_API_HOST=0.0.0.0
CB_API_PORT=4019
```

### Option 2: Use Host Networking (Alternative)

If the IP-based approach doesn't work, you can use Docker's host networking:

1. **Stop your current containers**:
   ```bash
   docker stop upbeat_maxwell
   ```

2. **Restart with host networking**:
   ```bash
   docker run --net=host -d razvanilin/chartbrew
   ```

3. **Update Strapi configuration to use localhost**:
   ```javascript
   chartbrew: {
     enabled: true,
     config: {
       apiUrl: 'http://localhost:4019',
       frontendUrl: 'http://localhost:4018',
     }
   },
   ```

## 🔄 Implementation Steps

### Step 1: Update Chartbrew Environment

1. **Copy the provided `chartbrew-docker-config.env` file** to your Docker container:
   ```bash
   docker cp chartbrew-docker-config.env upbeat_maxwell:/app/.env
   ```

2. **Restart the Chartbrew container**:
   ```bash
   docker restart upbeat_maxwell
   ```

### Step 2: Test the Connection

1. **Run the test script**:
   ```bash
   node fix-chartbrew-auth.js
   ```

2. **Generate a new API token** in Chartbrew:
   - Access http://**********:4018 in your browser
   - Log in and navigate to API Keys/Team Settings
   - Generate a new token
   - Test with: `node fix-chartbrew-auth.js YOUR_NEW_TOKEN`

### Step 3: Update Strapi CORS Configuration

Ensure Strapi's CORS settings allow connections from the Chartbrew container:

```javascript
// In backend/config/middlewares.ts
{
  name: 'strapi::cors',
  config: {
    origin: [
      'http://localhost:1337',
      'http://localhost:4018',
      'http://localhost:4019',
      'http://**********:4018',  // Add Chartbrew container IP
      'http://**********:4019'
    ],
    // Other CORS settings...
  }
},
```

## 🔍 Verifying the Fix

1. **Check Chartbrew API binding**:
   ```bash
   docker exec upbeat_maxwell env | grep CB_API_HOST
   # Should show: CB_API_HOST=0.0.0.0
   ```

2. **Test direct API access**:
   ```bash
   curl http://**********:4019
   # Should return: "Welcome to chartBrew server API"
   ```

3. **Test with valid token**:
   ```bash
   node fix-chartbrew-auth.js YOUR_NEW_TOKEN
   # Should show all green checkmarks
   ```

## 🚨 Important Notes

1. **Docker IP addresses can change** when containers are restarted. If this happens, you'll need to:
   - Run `docker inspect upbeat_maxwell | grep IPAddress` again
   - Update the IP in your Strapi plugin configuration

2. **Host networking is more stable** but less isolated. Consider using it for development environments.

3. **API tokens expire**. If authentication stops working, generate a new token in Chartbrew.

## 🔄 Troubleshooting

If you still have issues:

1. **Check container logs**:
   ```bash
   docker logs upbeat_maxwell
   ```

2. **Verify network connectivity**:
   ```bash
   # From host to container
   curl http://**********:4019
   
   # From container to host
   docker exec upbeat_maxwell curl http://host.docker.internal:1337
   ```

3. **Try host networking** as described in Option 2 if IP-based approach fails

4. **Check for firewall issues** that might block container-to-host communication
