#!/usr/bin/env node

/**
 * Chartbrew Token Authentication Test Script
 * Tests the specific API token authentication flow
 */

const http = require('http');
const https = require('https');
const url = require('url');

// Your API Token
const API_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************.bhebha1jtkmZevrRBbn7IWMF6NRjzHr1xhyCi5Zn7SE';

// Configuration
const CHARTBREW_API = 'http://localhost:4019';
const CHARTBREW_FRONTEND = 'http://localhost:4018';
const STRAPI_ORIGIN = 'http://localhost:1337';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(requestUrl, options = {}) {
  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(requestUrl);
    const client = parsedUrl.protocol === 'https:' ? https : http;
    
    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.path,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 10000
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data,
          url: requestUrl
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function testLoginEndpoint() {
  log('\n🔐 Testing Login Endpoint', 'bold');
  log('='.repeat(60), 'blue');

  try {
    // Test login endpoint without credentials first
    const result = await makeRequest(`${CHARTBREW_API}/login`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    log(`Login endpoint status: ${result.status}`, result.status === 200 ? 'green' : 'yellow');

    if (result.status === 200) {
      log('✅ Login endpoint is accessible', 'green');
    } else if (result.status === 405) {
      log('⚠️  Login endpoint exists but requires POST method', 'yellow');
    }
  } catch (error) {
    log(`❌ Login endpoint error: ${error.message}`, 'red');
  }

  // Test if we can get user info without token to see the error format
  try {
    const result = await makeRequest(`${CHARTBREW_API}/user`, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    log(`User endpoint without token: ${result.status}`, 'cyan');
    if (result.data) {
      log(`Response: ${result.data.substring(0, 200)}`, 'cyan');
    }
  } catch (error) {
    log(`User endpoint error: ${error.message}`, 'yellow');
  }
}

async function discoverAPIEndpoints() {
  log('\n🔍 Discovering API Endpoints', 'bold');
  log('='.repeat(60), 'blue');

  const commonPaths = [
    '/api',
    '/api/v1',
    '/api/v2',
    '/v1',
    '/v2',
    '/teams',
    '/user',
    '/projects',
    '/charts',
    '/connections',
    '/auth',
    '/login',
    '/team',
    '/project'
  ];

  for (const path of commonPaths) {
    try {
      const result = await makeRequest(`${CHARTBREW_API}${path}`, {
        headers: {
          'Authorization': `Bearer ${API_TOKEN}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (result.status !== 404) {
        log(`✅ Found endpoint: ${path} (Status: ${result.status})`, 'green');
        if (result.status === 200) {
          try {
            const data = JSON.parse(result.data);
            log(`   Response type: ${Array.isArray(data) ? 'Array' : typeof data}`, 'cyan');
          } catch (e) {
            log(`   Response: ${result.data.substring(0, 100)}...`, 'cyan');
          }
        }
      }
    } catch (error) {
      // Ignore connection errors for discovery
    }
  }
}

async function testTokenAuthentication() {
  log('\n🔑 Testing API Token Authentication', 'bold');
  log(`Token: ${API_TOKEN.substring(0, 20)}...`, 'cyan');
  log('='.repeat(60), 'blue');

  const endpoints = [
    { path: '/api/v1/teams', description: 'Get Teams' },
    { path: '/api/v1/user', description: 'Get User Info' },
    { path: '/api/v1/projects', description: 'Get Projects' },
    { path: '/api/v1/properties', description: 'Get Properties (v1)' },
    { path: '/api/properties', description: 'Get Properties (direct)' },
    { path: '/api/v1/connections', description: 'Get Connections' },
    { path: '/api/v1/charts', description: 'Get Charts' },
    // Try some alternative paths
    { path: '/teams', description: 'Get Teams (direct)' },
    { path: '/user', description: 'Get User (direct)' },
    { path: '/projects', description: 'Get Projects (direct)' },
    { path: '/charts', description: 'Get Charts (direct)' },
    { path: '/connections', description: 'Get Connections (direct)' }
  ];

  for (const endpoint of endpoints) {
    try {
      log(`\n📡 Testing: ${endpoint.description}`, 'yellow');
      log(`   URL: ${CHARTBREW_API}${endpoint.path}`, 'cyan');
      
      const result = await makeRequest(`${CHARTBREW_API}${endpoint.path}`, {
        headers: {
          'Authorization': `Bearer ${API_TOKEN}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Origin': STRAPI_ORIGIN,
          'User-Agent': 'Strapi-Chartbrew-Integration/1.0'
        }
      });
      
      log(`   Status: ${result.status}`, result.status === 200 ? 'green' : 'red');
      
      if (result.status === 200) {
        log(`   ✅ SUCCESS: Authentication working`, 'green');
        try {
          const jsonData = JSON.parse(result.data);
          if (Array.isArray(jsonData)) {
            log(`   📊 Response: Array with ${jsonData.length} items`, 'blue');
          } else if (typeof jsonData === 'object') {
            log(`   📊 Response: Object with keys: ${Object.keys(jsonData).slice(0, 5).join(', ')}`, 'blue');
          }
        } catch (e) {
          log(`   📊 Response: ${result.data.substring(0, 100)}...`, 'blue');
        }
      } else if (result.status === 401) {
        log(`   ❌ FAILED: Invalid or expired token`, 'red');
        log(`   💡 Solution: Generate a new API token in Chartbrew dashboard`, 'yellow');
      } else if (result.status === 403) {
        log(`   ❌ FAILED: Token valid but insufficient permissions`, 'red');
      } else if (result.status === 404) {
        log(`   ❌ FAILED: Endpoint not found`, 'red');
      } else {
        log(`   ⚠️  UNEXPECTED: Status ${result.status}`, 'yellow');
        log(`   Response: ${result.data.substring(0, 200)}`, 'cyan');
      }
      
      // Check CORS headers
      if (result.headers['access-control-allow-origin']) {
        log(`   🌐 CORS: ${result.headers['access-control-allow-origin']}`, 'green');
      } else {
        log(`   🌐 CORS: No Access-Control-Allow-Origin header`, 'yellow');
      }
      
    } catch (error) {
      log(`   ❌ CONNECTION ERROR: ${error.message}`, 'red');
      if (error.code === 'ECONNREFUSED') {
        log(`   💡 Solution: Make sure Chartbrew API server is running on port 4019`, 'yellow');
      }
    }
  }
}

async function testStrapiIntegrationFlow() {
  log('\n🔄 Testing Strapi Integration Flow', 'bold');
  log('='.repeat(60), 'blue');
  
  // Simulate the exact request that Strapi plugin would make
  try {
    log('\n📡 Simulating Strapi Plugin Authentication Request...', 'yellow');
    
    const result = await makeRequest(`${CHARTBREW_API}/api/v1/teams`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Origin': STRAPI_ORIGIN,
        'Referer': STRAPI_ORIGIN,
        'User-Agent': 'Mozilla/5.0 (Strapi Plugin)',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    if (result.status === 200) {
      log('✅ STRAPI INTEGRATION: Authentication successful!', 'green');
      log('   This token should work in your Strapi plugin', 'green');
      
      try {
        const teams = JSON.parse(result.data);
        if (Array.isArray(teams) && teams.length > 0) {
          log(`   📊 Found ${teams.length} team(s):`, 'blue');
          teams.forEach((team, index) => {
            log(`      ${index + 1}. ${team.name || team.id}`, 'cyan');
          });
        }
      } catch (e) {
        log('   📊 Response received but could not parse teams data', 'yellow');
      }
    } else {
      log(`❌ STRAPI INTEGRATION: Failed with status ${result.status}`, 'red');
      log(`   Response: ${result.data}`, 'cyan');
    }
    
  } catch (error) {
    log(`❌ STRAPI INTEGRATION: Connection failed - ${error.message}`, 'red');
  }
}

async function validateTokenFormat() {
  log('\n🔍 Token Validation', 'bold');
  log('='.repeat(60), 'blue');
  
  log(`Token Length: ${API_TOKEN.length} characters`, 'cyan');
  
  if (API_TOKEN.length < 50) {
    log('⚠️  Token seems too short for a Chartbrew API token', 'yellow');
  } else if (API_TOKEN.length > 500) {
    log('⚠️  Token seems too long for a Chartbrew API token', 'yellow');
  } else {
    log('✅ Token length looks reasonable', 'green');
  }
  
  if (/^[a-f0-9]+$/.test(API_TOKEN)) {
    log('✅ Token format: Valid hexadecimal string', 'green');
  } else {
    log('⚠️  Token format: Contains non-hexadecimal characters', 'yellow');
  }
}

function printConfiguration() {
  log('\n⚙️  Configuration for Strapi Plugin', 'bold');
  log('='.repeat(60), 'blue');
  
  log('Use these exact values in your Strapi Chartbrew plugin:', 'yellow');
  log('', 'reset');
  log('📍 Chartbrew Frontend URL:', 'cyan');
  log('   http://localhost:4018', 'green');
  log('', 'reset');
  log('📍 Chartbrew API URL:', 'cyan');
  log('   http://localhost:4019', 'green');
  log('', 'reset');
  log('📍 Strapi Backend URL:', 'cyan');
  log('   http://localhost:1337', 'green');
  log('', 'reset');
  log('🔑 API Token:', 'cyan');
  log(`   ${API_TOKEN}`, 'green');
}

async function main() {
  log('🚀 Chartbrew Token Authentication Test', 'bold');
  log(`🕒 ${new Date().toLocaleString()}`, 'blue');
  log('='.repeat(60), 'blue');

  await validateTokenFormat();
  await testLoginEndpoint();
  await discoverAPIEndpoints();
  await testTokenAuthentication();
  await testStrapiIntegrationFlow();
  printConfiguration();
  
  log('\n✨ Test Complete!', 'bold');
  log('\n📋 Summary:', 'yellow');
  log('• If all tests show ✅, your token is working correctly', 'green');
  log('• If you see ❌ 401 errors, generate a new token in Chartbrew', 'yellow');
  log('• If you see connection errors, ensure Chartbrew API is running', 'yellow');
  log('• Use the exact configuration values shown above in Strapi', 'blue');
}

// Run the test
main().catch(console.error);
