const http = require('http');

// Test various endpoints
const endpoints = [
  { path: '/user', description: 'User endpoint' },
  { path: '/team', description: 'Team endpoint' },
  { path: '/project', description: 'Project endpoint' },
  { path: '/charts', description: 'Charts endpoint' },
  { path: '/connections', description: 'Connections endpoint' }
];

function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 4019,
      path: endpoint.path,
      method: 'GET',
      timeout: 5000
    };

    console.log(`Testing ${endpoint.description} (${endpoint.path})...`);

    const req = http.request(options, (res) => {
      console.log(`${endpoint.description} - Status Code: ${res.statusCode}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (data) {
          console.log(`${endpoint.description} - Response: ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`);
        }
        resolve();
      });
    });

    req.on('error', (error) => {
      console.error(`${endpoint.description} - Error: ${error.message}`);
      resolve();
    });

    req.on('timeout', () => {
      console.error(`${endpoint.description} - Request timed out`);
      req.destroy();
      resolve();
    });

    req.end();
  });
}

async function testAllEndpoints() {
  console.log('Testing Chartbrew API endpoints without authentication...\n');
  
  for (const endpoint of endpoints) {
    await testEndpoint(endpoint);
    console.log(''); // Add a blank line between tests
  }
  
  console.log('\nAll tests completed!');
}

testAllEndpoints();
