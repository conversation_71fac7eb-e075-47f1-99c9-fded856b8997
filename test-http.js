const http = require('http');

const options = {
  hostname: 'localhost',
  port: 4019,
  path: '/',
  method: 'GET',
  timeout: 5000
};

console.log('Sending HTTP request to localhost:4019...');

const req = http.request(options, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response data:', data);
  });
});

req.on('error', (error) => {
  console.error('Error:', error.message);
});

req.on('timeout', () => {
  console.error('Request timed out');
  req.destroy();
});

req.end();
