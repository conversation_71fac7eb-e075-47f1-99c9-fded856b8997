const http = require('http');

const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************.4Iozhzd0jZmI2srQppinoB8QKweCnDeJ8CI1wngnK9E';

// Decode JWT token to check expiration
function decodeJWT(token) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
    return payload;
  } catch (e) {
    return null;
  }
}

function checkTokenExpiration() {
  console.log('🔍 Analyzing JWT Token...\n');
  
  const decoded = decodeJWT(TOKEN);
  
  if (!decoded) {
    console.log('❌ Invalid JWT token format');
    return false;
  }
  
  console.log('Token payload:');
  console.log(`- User ID: ${decoded.id}`);
  console.log(`- Email: ${decoded.email}`);
  console.log(`- Issued at: ${new Date(decoded.iat * 1000).toLocaleString()}`);
  console.log(`- Expires at: ${new Date(decoded.exp * 1000).toLocaleString()}`);
  
  const now = Math.floor(Date.now() / 1000);
  const isExpired = decoded.exp < now;
  
  if (isExpired) {
    console.log('\n❌ Token is EXPIRED');
    const expiredAgo = now - decoded.exp;
    console.log(`   Expired ${Math.floor(expiredAgo / 3600)} hours ago`);
    return false;
  } else {
    console.log('\n✅ Token is NOT expired');
    const expiresIn = decoded.exp - now;
    console.log(`   Expires in ${Math.floor(expiresIn / 3600)} hours`);
    return true;
  }
}

async function testTokenWithAPI() {
  console.log('\n🔌 Testing token with API...\n');
  
  const options = {
    hostname: 'localhost',
    port: 4019,
    path: '/user',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${TOKEN}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  };

  return new Promise((resolve) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response: ${data}`);
        
        if (res.statusCode === 200) {
          console.log('\n✅ Token is VALID and working!');
        } else if (res.statusCode === 401) {
          console.log('\n❌ Token is INVALID or EXPIRED');
          console.log('   Possible reasons:');
          console.log('   - Token was revoked');
          console.log('   - User account was deleted/disabled');
          console.log('   - Token format changed in newer Chartbrew version');
        } else {
          console.log(`\n⚠️ Unexpected status: ${res.statusCode}`);
        }
        
        resolve();
      });
    });

    req.on('error', (error) => {
      console.log(`❌ Connection error: ${error.message}`);
      resolve();
    });

    req.end();
  });
}

async function main() {
  console.log('🔍 Chartbrew Token Status Checker\n');
  console.log('='.repeat(50));
  
  const isValidFormat = checkTokenExpiration();
  await testTokenWithAPI();
  
  console.log('\n📋 Next Steps:');
  if (!isValidFormat) {
    console.log('1. Generate a new token in Chartbrew dashboard');
    console.log('2. Make sure to copy it immediately');
    console.log('3. Test the new token');
  } else {
    console.log('1. The token format is valid but API rejects it');
    console.log('2. Generate a new token in Chartbrew dashboard');
    console.log('3. Ensure you\'re logged into the correct account');
    console.log('4. Check if the user has proper permissions');
  }
  
  console.log('\nTo generate a new token:');
  console.log('- Open: http://localhost:4018');
  console.log('- Login and go to Team Settings > API Keys');
  console.log('- Generate new API key');
}

main();
