#!/usr/bin/env node

/**
 * Chartbrew Authentication Fix Script
 * This script helps you generate a new API token and test the connection
 */

const http = require('http');
const url = require('url');

// Configuration - Updated for Docker networking
const CHARTBREW_API = 'http://**********:4019';  // Docker container IP
const CHARTBREW_FRONTEND = 'http://**********:4018';
const STRAPI_ORIGIN = 'http://localhost:1337';

// Alternative localhost config (if using host networking)
const LOCALHOST_CHARTBREW_API = 'http://localhost:4019';
const LOCALHOST_CHARTBREW_FRONTEND = 'http://localhost:4018';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(requestUrl, options = {}) {
  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(requestUrl);
    const client = http;
    
    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.path,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 10000
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data,
          url: requestUrl
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function testToken(token) {
  if (!token) {
    log('❌ No token provided', 'red');
    return false;
  }

  log(`\n🔑 Testing Token: ${token.substring(0, 20)}...`, 'cyan');
  
  try {
    const result = await makeRequest(`${CHARTBREW_API}/user`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    if (result.status === 200) {
      log('✅ Token is VALID!', 'green');
      try {
        const userData = JSON.parse(result.data);
        log(`   User: ${userData.email || userData.name || 'Unknown'}`, 'cyan');
        log(`   ID: ${userData.id || 'Unknown'}`, 'cyan');
      } catch (e) {
        log('   User data received but could not parse', 'yellow');
      }
      return true;
    } else if (result.status === 401) {
      log('❌ Token is INVALID or EXPIRED', 'red');
      return false;
    } else {
      log(`⚠️  Unexpected status: ${result.status}`, 'yellow');
      return false;
    }
  } catch (error) {
    log(`❌ Connection error: ${error.message}`, 'red');
    return false;
  }
}

async function testStrapiEndpoints(token) {
  if (!token) return;

  log('\n🔄 Testing Strapi Integration Endpoints', 'bold');
  log('='.repeat(60), 'blue');

  const endpoints = [
    { path: '/user', description: 'User Info' },
    { path: '/team', description: 'Team Info' },
    { path: '/project', description: 'Projects' }
  ];

  for (const endpoint of endpoints) {
    try {
      const result = await makeRequest(`${CHARTBREW_API}${endpoint.path}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Origin': STRAPI_ORIGIN
        }
      });
      
      if (result.status === 200) {
        log(`✅ ${endpoint.description}: Working`, 'green');
        try {
          const data = JSON.parse(result.data);
          if (Array.isArray(data)) {
            log(`   Found ${data.length} items`, 'cyan');
          } else if (typeof data === 'object') {
            log(`   Response keys: ${Object.keys(data).slice(0, 3).join(', ')}`, 'cyan');
          }
        } catch (e) {
          log('   Response received', 'cyan');
        }
      } else {
        log(`❌ ${endpoint.description}: Status ${result.status}`, 'red');
      }
    } catch (error) {
      log(`❌ ${endpoint.description}: ${error.message}`, 'red');
    }
  }
}

function printInstructions() {
  log('\n📋 How to Generate a New API Token', 'bold');
  log('='.repeat(60), 'blue');
  log('', 'reset');
  log('1. Open your browser and go to:', 'yellow');
  log(`   ${CHARTBREW_FRONTEND}`, 'green');
  log('', 'reset');
  log('2. Log in to Chartbrew (or create an account if needed)', 'yellow');
  log('', 'reset');
  log('3. Once logged in, look for one of these options:', 'yellow');
  log('   • Profile/Account Settings', 'cyan');
  log('   • Team Settings', 'cyan');
  log('   • API Keys', 'cyan');
  log('   • Integrations', 'cyan');
  log('', 'reset');
  log('4. Find "API Keys" or "Generate API Token" section', 'yellow');
  log('', 'reset');
  log('5. Click "Generate New API Key" or "Create Token"', 'yellow');
  log('', 'reset');
  log('6. Copy the generated token immediately!', 'red');
  log('   (You won\'t be able to see it again)', 'red');
  log('', 'reset');
  log('7. Test the new token by running:', 'yellow');
  log('   node fix-chartbrew-auth.js YOUR_NEW_TOKEN', 'green');
}

function printStrapiConfig(token) {
  if (!token) return;
  
  log('\n⚙️  Strapi Plugin Configuration', 'bold');
  log('='.repeat(60), 'blue');
  log('', 'reset');
  log('Use these values in your Strapi Chartbrew plugin:', 'yellow');
  log('', 'reset');
  log('📍 Chartbrew API URL:', 'cyan');
  log(`   ${CHARTBREW_API}`, 'green');
  log('', 'reset');
  log('📍 Chartbrew Frontend URL:', 'cyan');
  log(`   ${CHARTBREW_FRONTEND}`, 'green');
  log('', 'reset');
  log('🔑 API Token:', 'cyan');
  log(`   ${token}`, 'green');
}

async function main() {
  const token = process.argv[2];
  
  log('🔧 Chartbrew Authentication Fix Tool', 'bold');
  log(`🕒 ${new Date().toLocaleString()}`, 'blue');
  log('='.repeat(60), 'blue');

  if (!token) {
    log('\n⚠️  No API token provided', 'yellow');
    printInstructions();
    return;
  }

  const isValid = await testToken(token);
  
  if (isValid) {
    await testStrapiEndpoints(token);
    printStrapiConfig(token);
    log('\n✅ SUCCESS! Your token is working correctly.', 'green');
    log('You can now use this token in your Strapi plugin.', 'green');
  } else {
    log('\n❌ Token is not working. Please generate a new one.', 'red');
    printInstructions();
  }
}

// Run the script
main().catch(console.error);
