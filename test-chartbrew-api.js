#!/usr/bin/env node

/**
 * Chartbrew API Test Script
 * Tests direct connection to Chartbrew API without Strapi
 */

const http = require('http');
const url = require('url');
const readline = require('readline');

// Configuration - Using localhost (port forwarding)
const CHARTBREW_API = 'http://localhost:4019';
const CHARTBREW_FRONTEND = 'http://localhost:4018';

// Alternative Docker container IP (if port forwarding doesn't work)
// const CHARTBREW_API = 'http://**********:4019';
// const CHARTBREW_FRONTEND = 'http://**********:4018';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(requestUrl, options = {}) {
  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(requestUrl);
    const client = http;
    
    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.path,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 10000
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data,
          url: requestUrl
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function testBasicConnection() {
  log('\n🔌 Testing Basic API Connection', 'bold');
  log('='.repeat(60), 'blue');
  
  try {
    const result = await makeRequest(`${CHARTBREW_API}`);
    log(`API Root Status: ${result.status}`, result.status === 200 ? 'green' : 'yellow');
    log(`Response: ${result.data}`, 'cyan');
    
    if (result.status === 200) {
      log('✅ Basic API connection successful!', 'green');
      return true;
    } else {
      log('⚠️ API responded but with unexpected status code', 'yellow');
      return false;
    }
  } catch (error) {
    log(`❌ API Connection Error: ${error.message}`, 'red');
    log('', 'reset');
    log('Possible causes:', 'yellow');
    log('1. Docker container not running', 'yellow');
    log('2. Incorrect IP address (current: ' + CHARTBREW_API + ')', 'yellow');
    log('3. API server not binding to all interfaces (should use 0.0.0.0)', 'yellow');
    log('4. Network/firewall blocking the connection', 'yellow');
    return false;
  }
}

async function testEndpoints() {
  log('\n🔍 Testing API Endpoints Without Authentication', 'bold');
  log('='.repeat(60), 'blue');
  
  const endpoints = [
    '/user',
    '/team',
    '/project',
    '/charts',
    '/connections',
    '/login'
  ];
  
  for (const endpoint of endpoints) {
    try {
      const result = await makeRequest(`${CHARTBREW_API}${endpoint}`);
      
      if (result.status === 401) {
        log(`✅ ${endpoint}: Returns 401 (Authentication Required)`, 'green');
      } else if (result.status === 404) {
        log(`❌ ${endpoint}: Returns 404 (Not Found)`, 'red');
      } else {
        log(`⚠️ ${endpoint}: Returns ${result.status}`, 'yellow');
      }
    } catch (error) {
      log(`❌ ${endpoint}: Error - ${error.message}`, 'red');
    }
  }
}

async function testToken(token) {
  if (!token) {
    log('\n⚠️ No token provided, skipping authentication test', 'yellow');
    return;
  }

  log('\n🔑 Testing Authentication with Token', 'bold');
  log('='.repeat(60), 'blue');
  
  const endpoints = [
    { path: '/user', description: 'User Info' },
    { path: '/team', description: 'Team Info' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      const result = await makeRequest(`${CHARTBREW_API}${endpoint.path}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      if (result.status === 200) {
        log(`✅ ${endpoint.description}: Authentication successful!`, 'green');
        try {
          const data = JSON.parse(result.data);
          if (Array.isArray(data)) {
            log(`   Found ${data.length} items`, 'cyan');
          } else if (typeof data === 'object') {
            log(`   Response keys: ${Object.keys(data).slice(0, 3).join(', ')}`, 'cyan');
          }
        } catch (e) {
          log(`   Response: ${result.data.substring(0, 100)}...`, 'cyan');
        }
      } else if (result.status === 401) {
        log(`❌ ${endpoint.description}: Invalid or expired token`, 'red');
      } else {
        log(`⚠️ ${endpoint.description}: Unexpected status ${result.status}`, 'yellow');
      }
    } catch (error) {
      log(`❌ ${endpoint.description}: Error - ${error.message}`, 'red');
    }
  }
}

async function testFrontend() {
  log('\n🖥️ Testing Frontend Connection', 'bold');
  log('='.repeat(60), 'blue');
  
  try {
    const result = await makeRequest(`${CHARTBREW_FRONTEND}`);
    
    if (result.status === 200) {
      log('✅ Frontend connection successful!', 'green');
      log(`   Content length: ${result.data.length} characters`, 'cyan');
    } else {
      log(`⚠️ Frontend returned status ${result.status}`, 'yellow');
    }
  } catch (error) {
    log(`❌ Frontend Connection Error: ${error.message}`, 'red');
    log('', 'reset');
    log('Possible causes:', 'yellow');
    log('1. Docker container not running', 'yellow');
    log('2. Incorrect IP address (current: ' + CHARTBREW_FRONTEND + ')', 'yellow');
    log('3. Frontend server not started', 'yellow');
    log('4. Network/firewall blocking the connection', 'yellow');
  }
}

async function main() {
  const token = process.argv[2];
  
  log('🚀 Chartbrew API Direct Test', 'bold');
  log(`🕒 ${new Date().toLocaleString()}`, 'blue');
  log('='.repeat(60), 'blue');
  
  const basicConnected = await testBasicConnection();
  
  if (basicConnected) {
    await testEndpoints();
    await testToken(token);
    await testFrontend();
    
    if (!token) {
      log('\n📋 Next Steps:', 'bold');
      log('1. Generate a token in Chartbrew dashboard', 'yellow');
      log('2. Run this script with the token:', 'yellow');
      log(`   node test-chartbrew-api.js YOUR_TOKEN`, 'cyan');
    }
  } else {
    log('\n📋 Fix the basic connection first:', 'bold');
    log('1. Verify Docker container is running:', 'yellow');
    log('   docker ps | findstr chartbrew', 'cyan');
    log('2. Check the container IP:', 'yellow');
    log('   docker inspect upbeat_maxwell | findstr IPAddress', 'cyan');
    log('3. Update the IP in this script if needed', 'yellow');
    log('4. Ensure Chartbrew API binds to all interfaces (0.0.0.0)', 'yellow');
  }
}

// Run the script
main().catch(console.error);
