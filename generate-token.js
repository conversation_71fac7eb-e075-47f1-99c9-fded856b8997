const http = require('http');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Configuration
const CHARTBREW_API = 'http://localhost:4019';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }

    req.end();
  });
}

async function loginAndGenerateToken(email, password) {
  log('\n🔑 Attempting to login and generate token...', 'bold');
  
  const options = {
    hostname: 'localhost',
    port: 4019,
    path: '/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  const postData = JSON.stringify({
    email: email,
    password: password
  });

  try {
    const result = await makeRequest(options, postData);
    
    if (result.status === 200) {
      try {
        const responseData = JSON.parse(result.data);
        if (responseData.token) {
          log('✅ Login successful!', 'green');
          log(`Token: ${responseData.token}`, 'cyan');
          return responseData.token;
        } else {
          log('⚠️ Login successful but no token returned', 'yellow');
          return null;
        }
      } catch (e) {
        log('⚠️ Error parsing response', 'yellow');
        log(result.data, 'cyan');
        return null;
      }
    } else {
      log(`❌ Login failed with status ${result.status}`, 'red');
      log(result.data, 'cyan');
      return null;
    }
  } catch (error) {
    log(`❌ Request error: ${error.message}`, 'red');
    return null;
  }
}

async function testToken(token) {
  log('\n🔍 Testing token...', 'bold');
  
  const options = {
    hostname: 'localhost',
    port: 4019,
    path: '/user',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  try {
    const result = await makeRequest(options);
    
    if (result.status === 200) {
      log('✅ Token is valid!', 'green');
      try {
        const userData = JSON.parse(result.data);
        log(`User: ${userData.email || 'Unknown'}`, 'cyan');
        log(`ID: ${userData.id || 'Unknown'}`, 'cyan');
        return true;
      } catch (e) {
        log('⚠️ Error parsing user data', 'yellow');
        return true;
      }
    } else {
      log(`❌ Token validation failed with status ${result.status}`, 'red');
      log(result.data, 'cyan');
      return false;
    }
  } catch (error) {
    log(`❌ Request error: ${error.message}`, 'red');
    return false;
  }
}

function printInstructions() {
  log('\n📋 How to Generate a Token in Chartbrew Dashboard', 'bold');
  log('='.repeat(60), 'blue');
  log('', 'reset');
  log('1. Open your browser and go to:', 'yellow');
  log('   http://localhost:4018', 'green');
  log('', 'reset');
  log('2. Log in to Chartbrew', 'yellow');
  log('', 'reset');
  log('3. Go to Team Settings > API Keys', 'yellow');
  log('', 'reset');
  log('4. Click "Generate new API key"', 'yellow');
  log('', 'reset');
  log('5. Copy the generated token immediately', 'yellow');
  log('', 'reset');
  log('6. Test the token with:', 'yellow');
  log('   node test-with-token.js YOUR_TOKEN', 'green');
}

async function main() {
  log('🚀 Chartbrew Token Generator', 'bold');
  log('='.repeat(60), 'blue');
  
  log('\nThis tool will help you generate and test a Chartbrew API token.', 'cyan');
  log('You can either login with your credentials or generate a token manually.', 'cyan');
  
  rl.question('\nDo you want to try logging in? (y/n): ', async (answer) => {
    if (answer.toLowerCase() === 'y') {
      rl.question('Email: ', (email) => {
        rl.question('Password: ', async (password) => {
          const token = await loginAndGenerateToken(email, password);
          
          if (token) {
            await testToken(token);
            log('\n✅ Use this token in your Strapi plugin configuration:', 'green');
            log(token, 'cyan');
          } else {
            printInstructions();
          }
          
          rl.close();
        });
      });
    } else {
      printInstructions();
      
      rl.question('\nIf you already have a token, enter it to test (or press Enter to skip): ', async (token) => {
        if (token) {
          await testToken(token);
        }
        
        rl.close();
      });
    }
  });
}

main();
