# Chartbrew v4 + Strapi 5 Authentication Troubleshooting Guide

## 🔍 Current Status

✅ **Chartbrew API Server**: Running correctly on port 4019  
✅ **Chartbrew Frontend**: Running correctly on port 4018  
✅ **API Endpoints Discovered**: `/user`, `/team`, `/project`  
❌ **API Token**: Invalid/Expired (401 errors)  

## 🚨 Main Issue

The current API token is **invalid or expired**. All authentication attempts return 401 status codes.

## 🔧 Step-by-Step Fix

### Step 1: Access Chartbrew Dashboard

1. Open your browser and navigate to: **http://localhost:4018**
2. You should see the Chartbrew login/dashboard page

### Step 2: Log In or Create Account

- If you have an existing account: **Log in**
- If you need to create an account: **Sign up**

### Step 3: Generate New API Token

1. Once logged in, look for one of these menu options:
   - **Profile Settings**
   - **Account Settings** 
   - **Team Settings**
   - **API Keys**
   - **Integrations**

2. Navigate to the **API Keys** or **API Tokens** section

3. Click **"Generate New API Key"** or **"Create API Token"**

4. **IMPORTANT**: Copy the token immediately! You won't be able to see it again.

### Step 4: Test the New Token

Run the authentication test script with your new token:

```bash
node fix-chartbrew-auth.js YOUR_NEW_TOKEN_HERE
```

Expected output for a valid token:
```
✅ Token is VALID!
   User: <EMAIL>
   ID: 1
✅ User Info: Working
✅ Team Info: Working  
✅ Projects: Working
```

### Step 5: Update Strapi Plugin Configuration

Once you have a working token, use these exact values in your Strapi Chartbrew plugin:

- **Chartbrew API URL**: `http://localhost:4019`
- **Chartbrew Frontend URL**: `http://localhost:4018`
- **API Token**: `YOUR_NEW_VALID_TOKEN`

## 🔍 Verification Commands

### Check if Chartbrew services are running:
```bash
docker ps
```

### Test API server connectivity:
```bash
curl http://localhost:4019
# Should return: "Welcome to chartBrew server API"
```

### Test frontend connectivity:
```bash
curl -I http://localhost:4018
# Should return: HTTP/1.1 200 OK
```

### Test API endpoints without token:
```bash
curl http://localhost:4019/user
# Should return: "Token is missing."
```

## 🐛 Common Issues & Solutions

### Issue: "Cannot connect to Chartbrew"
**Solution**: Ensure Docker containers are running:
```bash
docker-compose up -d
```

### Issue: "Frontend not loading"
**Solution**: Check if port 4018 is accessible and not blocked by firewall

### Issue: "API returns 404 for all endpoints"
**Solution**: The API server might not be fully started. Wait a few minutes and try again.

### Issue: "Token still returns 401 after generation"
**Solutions**:
1. Make sure you copied the complete token
2. Check for extra spaces or characters
3. Generate a new token
4. Verify you're using the correct user account

### Issue: "CORS errors in browser"
**Solution**: This is expected for direct API calls from browser. Use the Node.js test scripts instead.

## 📋 Correct API Endpoints

Based on discovery, these are the working endpoints:

- ✅ `/user` - Get user information
- ✅ `/team` - Get team information  
- ✅ `/project` - Get projects
- ❌ `/api/v1/*` - These paths don't exist in this Chartbrew version

## 🔄 Next Steps After Token Fix

1. **Test the connection** with the new token
2. **Update Strapi plugin** configuration with the new token
3. **Test Strapi integration** by accessing the plugin in Strapi admin
4. **Verify data flow** between Strapi and Chartbrew

## 📞 Additional Help

If you continue to have issues:

1. Check Docker container logs:
   ```bash
   docker logs [CONTAINER_NAME] --tail 50
   ```

2. Verify network connectivity between containers

3. Ensure all environment variables are correctly set

4. Check if there are any firewall or antivirus blocking the connections

## 🎯 Success Criteria

You'll know the authentication is working when:

- ✅ `node fix-chartbrew-auth.js YOUR_TOKEN` shows all green checkmarks
- ✅ Strapi admin shows the Chartbrew plugin without errors
- ✅ You can create connections and charts in the Strapi plugin
- ✅ Data flows correctly from Strapi to Chartbrew dashboards
